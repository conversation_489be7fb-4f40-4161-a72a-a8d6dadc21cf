<?php

namespace Database\Seeders;

use App\Models\Agence;
use Illuminate\Database\Seeder;

class AgenceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $moroccanAgencies = [
            ['nom_fr' => 'CTM', 'nom_ar' => 'شركة النقل المغربية', 'path' => 'ctm-logo.png'],
            ['nom_fr' => 'Supratours', 'nom_ar' => 'سوبراتورز', 'path' => 'supratours-logo.png'],
            ['nom_fr' => 'ALSA', 'nom_ar' => 'ألسا', 'path' => 'alsa-logo.png'],
            ['nom_fr' => 'Ghazala', 'nom_ar' => 'غزالة', 'path' => 'ghazala-logo.png'],
            ['nom_fr' => 'SATAS', 'nom_ar' => 'ساتاس', 'path' => 'satas-logo.png'],
            ['nom_fr' => '<PERSON>ull<PERSON> du Maroc', 'nom_ar' => 'بولمان المغرب', 'path' => 'pullman-logo.png'],
            ['nom_fr' => 'Trans Ghazala', 'nom_ar' => 'ترانس غزالة', 'path' => 'trans-ghazala-logo.png'],
            ['nom_fr' => 'Nejme Chamal', 'nom_ar' => 'نجمة الشمال', 'path' => 'nejme-chamal-logo.png'],
            ['nom_fr' => 'Tasla', 'nom_ar' => 'تاسلا', 'path' => 'tasla-logo.png'],
            ['nom_fr' => 'Stareo', 'nom_ar' => 'ستاريو', 'path' => 'stareo-logo.png'],
        ];

        foreach ($moroccanAgencies as $agency) {
            Agence::firstOrCreate(
                ['nom_fr' => $agency['nom_fr']],
                $agency
            );
        }
    }
}
