<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // Seed basic data first
        $this->call([
            AdminSeeder::class,
            VilleSeeder::class,
            AgenceSeeder::class,
            VoyageSeeder::class,
        ]);

        // Create users
        \App\Models\User::factory(50)->create();

        // Create some reservations
        \App\Models\Reservation::factory(100)->create();

        $this->command->info('Database seeded successfully with Moroccan travel data!');
        $this->command->info('Default admin credentials:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
    }
}
