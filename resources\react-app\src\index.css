/*============= RESET STYLE =============

/*-- iPhone X Remove Gutters
Ignore W3C Validation error for this style. --*/
html {
  padding: env(safe-area-inset);
}
::-webkit-scrollbar {
  width: 12px;
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(transparent, rgb(14, 149, 190));
  border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgb(7, 56, 121);
}
/*-- Body Resets --*/
body {
  font-family: "Lato", sans-serif;
  color: #505962;
  justify-content: center;
  overflow: overlay;
}

/* Loading */
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f8f8f8ad;
}
.loader {
  left: 43%;
  top: 35%;
  z-index: 1000;
  position: absolute;
}

/*============= NAVIGATION =============*/
.accordion {
  position: absolute;
  right: 0px;
  width: 250px;
  text-decoration: none;
  text-transform: initial;
  text-align: center;
}
.accSum {
  color: rgb(79, 79, 189);
  font-weight: bolder;
  font-family: Verdana, Geneva, Tahoma, sans-serif;
}
.fa-trash-alt {
  color: red;
  cursor: pointer;
}
.accTotal {
  margin-left: 20px;
  font-family: sans-serif;
  color: rgb(79, 79, 189);
}
.navbar {
  background-color: #3c5a9e;
  border-radius: 0.5px;
}
.inlog {
  font-size: 13px;
}
.outlog {
  font-size: 13px;
}
.outlog i {
  font-size: 15px;
  margin-top: 5px;
}
.navbar-brand img {
  height: 2rem;
}

.navbar-dark {
  font-size: 20px;
  font-weight: 700;
  color: #00b3ff;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
}
.navbar-nav li {
  padding-right: 3rem;
}
.navbar-nav .nav-link {
  padding-top: 0.8rem;
  color: white !important;
}
.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  transition: all 0.5s;
  color: rgb(223, 215, 215);
}
.dropdown-menu {
  transform: translate(-60%);
}
.btnlogin {
  transform: translate(20%);
  border-radius: 24px;
  background-color: #275efe;
  padding: 5px 18px;
  font-family: Roboto, sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: white;
  border: none;
  box-shadow: none;
  outline: none !important;
  cursor: pointer;
}
.btnlogin:hover {
  background-color: #0d3bc4;
}
.cart-icon {
  font-size: 13px;
  cursor: pointer;
}
.cart-icon span {
  position: absolute;
  top: 6px;
  right: -3px;
}
/*============= LANDING PAGE =============*/

/*-- Fixed Landing Page Section --*/
.landing {
  height: 100vh;
  width: 100vh;
}
.home-inner {
  background-image: url("./images/2.jpg");
  /* background-size: contain; */
  background-repeat: no-repeat;
}
/*-- Landing Page Content --*/

/*--Select box--*/
.Vcontainer {
  margin-top: 15%;
  text-align: center;
  z-index: -1;
}
.select-box {
  padding-bottom: 15px;
}

.selected,
.Date,
.Search-btn {
  background-color: white;
  color: white;
  text-align: center;
  width: 450px;
  border: none;
  margin-bottom: 15px;
  justify-content: center;
}
.selected {
  border-radius: 9px;
}
.Date {
  padding: 14px 0px;
  border-radius: 9px;
  color: black;
}
.dateError {
  background-color: white;
  color: red;
  text-align: center;
  font-weight: bolder;
  position: relative;
  width: 200px;
  margin-left: 40%;
}
.Search-btn {
  background-color: #093499;
  padding: 14px 30px;
  border-radius: 20px;
  display: inline-block;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  color: #ffffff;
  text-align: center;
  transition: all 0.5s;
  outline: none !important;
}
.Search-btn:hover {
  background-color: #1f58dd;
}
/* .slide{
  height: 250px;
  margin: auto;
  position: relative;
  width: 90%;
  display: grid;
  place-items: center;
}
.slide-track{
  display: flex;
  width: calc(250px*18);
}
.slide{
  height: 200px;
  width: 250px;
  display: flex;
  align-items: center;
  padding: 15px;
  perspective: 100px;
}
.slide img{
width: 100%;
border-radius: 100px;
animation: scroll 40s linear infinite;
}
.slide img:hover{
  transform: translateZ(20px);
  }
  @keyframes scroll{
    0%{
      transform: translate(0);
    }
    100%{
      transform: translateX(calc(-250px*9));
    }
  }
.slider::before,
.slider::after{
  background: linear-gradient(transparent,rgba(89, 120, 177, 0.336),rgba(5, 62, 116, 0.733));
  content: '';
  height: 100%;
  position: absolute;
  width: 15%;
  z-index: 2;
}
.slider::before{
  left: 0;
  top: 0;
}
.slider::after{
  right: 0;
  top: 0;
} */
/*============= ABOUT SECTION =============*/
.about {
  width: 100%;
  padding: 50px;
  margin-bottom: 0px;
  text-align: center;
  list-style: none;
  background-color: #3c5a9e;
  color: white;
}
.about h2 {
  color: white;
  font-weight: bolder;
}
.about table {
  width: 100%;
  height: 100%;
}
.about_items {
  list-style: none;
  padding: 20px;
}
.about_items td {
  padding-left: 10px;
}
.about-text {
  border-radius: 10px;
  width: 200px;
}
.about img {
  width: 80px;
  border-radius: 40%;
  background-color: white;
  padding: 15px;
}
.about-body {
  margin-top: 10px;
  font-weight: bolder;
}
.about-title {
  color: white;
  font-weight: bolder;
  background-color: #0976f3;
  font-size: 17px;
  width: 140px;
  border-radius: 2px;
  text-align: center;
}
/*============= Card SECTION =============*/

.cards {
  width: 100%;
  padding: 50px;
  padding-left: 150px;
  margin-bottom: 0px;
  text-align: center;
  background-color: rgba(243, 241, 241, 0.664);
  flex-direction: column;
}

.cards h2 {
  color: #0976f3;
  font-weight: bolder;
}
.cards_items {
  list-style: none;
  padding: 20px;
  display: flex;
}

.cards_items img {
  width: 300px;
  cursor: pointer;
  border-radius: 20px;
  padding: 10px;
  margin-right: 10px;
  transform: scale(1);
  height: 200px;
}
.cards_items img:hover {
  transform: scale(1.05);
}
.cards_items section {
  background-color: white;
  margin-left: 50px;
  text-align: center;
  font-weight: bolder;
}

.cards_item {
  padding-left: 10px;
  box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}
.card-text {
  border-radius: 10px;
  width: 300px;
}
.card-title {
  color: white;
  font-weight: bolder;
  background-color: #0976f3;
  font-size: 17px;
  width: 160px;
  border-radius: 2px;
  text-align: center;
}
/*-- Scrollspy Offset --*/

/*============= PORTFOLIO SECTION =============*/

/*============= TESTIMONIALS SECTION =============*/

/*============= CONTACT SECTION =============*/
/*==============SE CONNECTER=================*/
.none {
  display: none;
}
.profile {
  font-size: 20px;
}
.dropdown-item {
  cursor: pointer;
}
.notfound {
  background-color: #0e7077;
  color: white;
  border-radius: 10px;
  box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
  text-align: center;
}
.sad {
  font-size: 100px;
}
.liste-found {
  text-align: center;
}
/*============Second PAGE ==================*/
.secondpage {
  background-color: rgba(74, 134, 247, 0.418);
  background-repeat: no-repeat;
}
.secondpage table {
  margin-top: 2%;
}
.secondpage table td {
  color: black;
  border-collapse: separate;
}
.transport {
  width: 200px;
}
.bus-img {
  width: 160px;
}
.ligne {
  width: 100px;
  padding: 20px;
}
.ligne div {
  margin-top: 20px;
}
.locale {
  text-align: center;
  width: 530px;
}
.locale div {
  margin-top: 10px;
}

.depart div {
  margin-top: 28px;
  padding-left: 5px;
}

.prix div {
  margin-top: 28px;
  padding-left: 20px;
}
.reservation-btn {
  background-color: #a0b7ee;
  padding: 14px 30px;
  border-radius: 20px;
  display: inline-block;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  margin-left: 10px;
  margin-top: 14px;
  transition: all 0.5s;
  outline: none !important;
}
.reservation-btn:hover {
  background-color: #134acc;
}
.voyage {
  text-align: center;

  background-image: url("./images/ticket.jpg");
  border-radius: 30px;
  background-position: 200px -100px;
  background-size: cover;

  box-shadow: 0 5px 8px 0 rgba(17, 137, 192, 0.493),
    0 7px 20px 0 rgba(0, 0, 0, 0.2);
  grid-template-columns: 1fr 1fr 1fr;
}
.title-voyage {
  text-align: center;
  font-weight: 900;
  color: rgb(4, 55, 121);
  background-color: white;
  width: 500px;
  margin-left: 35%;
  border-radius: 10px;
  margin-top: 10px;
  font-family: "Gill Sans", "Gill Sans MT", Calibri, "Trebuchet MS", sans-serif;
}
.arrow {
  width: 100px;
  transform: rotate(270deg);
  margin: 0px;
  padding: 0px;
}
.allvoyages {
  width: 100%;
  padding: 100px;
  text-align: center;
  vertical-align: middle;
  margin-bottom: 20px;
}
.voyage tr {
  text-align: center;
  vertical-align: middle;
}
.voyage td {
  border-left: solid 3px dotted !important;
  padding: 20px 0px;
  border-left-color: rgb(11, 130, 241) !important;
  text-align: center;
  vertical-align: middle;
}
.bus-img {
  height: 10vh;
}
.select-box-second {
  display: flex;
}
.selected-second,
.Date-second,
.Search-btn-second {
  background-color: white;
  color: white;
  text-align: center;
  margin-top: 30%;
  margin-left: 60px;
  width: 200px;
  border: none;
  margin-bottom: 15px;
  justify-content: center;
}
.Date-second {
  padding: 15.77px 0px;

  color: black;
}
.Search-btn-second {
  background-color: #a0b7ee;
  padding: 14px 30px;
  border-radius: 20px;
  display: inline-block;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  transition: all 0.5s;
  outline: none !important;
}
.Search-btn-second:hover {
  background-color: #134acc;
}
.Modifier {
  background-image: url("./images/g.jpg");
  background-repeat: repeat-x;
  background-position-y: -250px;
}
.btn-reserver {
  background-color: #a0b7ee;
  padding: 14px 30px;
  border-radius: 20px;
  display: inline-block;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  color: #ffffff;
  text-align: center;
  transition: all 0.5s;
  margin-left: 20px;
  outline: none !important;
}
.btn-reserver:hover {
  background-color: #134acc;
}
/*========== Reservation ============*/
.ticket {
  width: 340px;
  background: url("./images/ticket.jpg");
  background-position: -45px -80px;
  margin-top: 4%;

  color: black;
  box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
  list-style: none;
  font-weight: bold;
  padding-left: 0;
  margin-left: 100px;
  border-radius: 20px;
  justify-content: center;
}
.reservation {
  text-align: center;
  background: url("./images/reserver.jpg");
  margin-top: 150px;
  box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
  border-radius: 50px;
  grid-template-columns: 1fr;
  width: 600px;
  padding: 10px;
}
.reservation-none {
  display: none;
}
.reservation table {
  margin-left: 0px;
}
.reservation td {
  padding: 20px;
  padding-top: 10px;
  font-weight: bolder;
  color: black;
}
.reservation input {
  padding: 3px 10px;
  border-radius: 10px;
  outline: none !important;
  border: none;
}
.reservation label {
  margin-left: 50px;
  color: rgb(51, 20, 136);
}
.reservation li {
  padding-top: 5px;
}
.btn-confirm {
  background-color: #a0b7ee;
  padding: 5px 20px;
  border-radius: 20px;
  display: inline-block;
  border: none;
  border-radius: 20px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  color: #ffffff;
  text-align: center;
  transition: all 0.5s;
  margin-left: 40px;
  outline: none !important;
}
.btn-confirm:hover {
  background-color: #134acc;
}
.btn-guest,
.btn-client {
  padding: 0px 10px;
  margin-left: 120px;
  place-items: center;
  background-color: #a0b7ee;
  text-transform: uppercase;
  font-weight: bolder;
  margin-bottom: 100px;
  border-radius: 10px;
  border: none;
}
.userRes {
  text-align: center;
  margin-left: 20.5%;
}
.complet-res {
  color: white;
  text-decoration: none;
}
.complet-res a {
  color: white;
  background-color: rgb(12, 183, 206);
  text-decoration: none;
  border-radius: 4px;
  padding: 2px 10px;
  text-align: center;
}
.complet-res a:hover {
  background-color: rgb(4, 86, 97);
  transition: all 0.5s;
}
/* =========== paiement ============ */
.paiement {
  text-align: center;
  margin-top: 10px;
  margin-left: 100px;
  justify-content: center;
  transition: all 0.5s;
  background-color: red;
}
.payform input {
  padding: 3px 10px;
  border-radius: 10px;
  outline: none !important;
  border: none;
  width: 300px;
  text-align: center;
  padding-top: 10px;
  margin-bottom: 20px;
}
.payform label {
  margin-left: 20px;
  color: rgb(170, 169, 173);
}
.payform img {
  width: 40px;
}
.payform i {
  font-size: 30px;
}
.payform p {
  background-color: rgb(0, 102, 255);
  border-radius: 200px;
  color: white;
  width: 100px;
  margin-left: 150px;
}
.paysur {
  background-color: rgb(0, 102, 255);
  border-radius: 50px;
  color: white;
  width: 300px;
  margin-left: 50px;
}
.paiement-none {
  display: none;
  text-align: center;
  margin-top: 10px;
  margin-left: 340px;
}
.close-btn {
  cursor: pointer;
  font-size: 40px;
  color: rgb(12, 129, 158);
  position: absolute;
  margin-left: 90%;
}
.showguest {
  text-align: center;
  padding: 50px;
  padding-bottom: 2px;
  border-radius: 100px;
}
.ModalGuest {
  margin-top: 7%;
}

.modal-title {
  font-size: bolder;
  text-align: center;
  text-transform: uppercase;
  font-size: 30px;
}
.modal-content {
  border-radius: 20px;
  color: black;
  font-weight: bolder;
  background: url("./images/reserver.jpg");
  font-family: "Trebuchet MS", "Lucida Sans Unicode", "Lucida Grande",
    "Lucida Sans", Arial, sans-serif;
  background-repeat: no-repeat;
  background-size: cover;
  text-transform: uppercase;
  font-weight: bolder;
  background-position: -80px 0px;
}
.confirmpay {
  font-weight: bolder;
  color: black;
  margin-top: 10px;
}
/*============= MEDIA QUERIES =============*/

/*===========================================*/
@media (max-width: 500px) {
}
/* ============== Footer ==================*/
.footer-up {
  background-color: black;

  padding: 50px;
  padding-right: 50px;
  color: white;
  text-transform: none;
  text-align: center;
  font-weight: bolder;
  font-family: Verdana, Geneva, Tahoma, sans-serif;
}

.footer-up h5 {
  text-transform: uppercase;
}

.footer-up section {
  color: white;
}
.socialmedia .me-4 {
  text-align: center;
  justify-content: center;
  padding: 10px;
  font-size: 20px;
}

.footer-up img {
  width: 100px;
}
.footer-up img:hover {
  cursor: pointer;
}
.footer-up a {
  text-decoration: none;
}
.footer-up a:hover {
  cursor: pointer;
}

.footer-down {
  background-color: rgb(79, 79, 189);
  font-weight: bolder;
  color: white;
  text-align: center;
}
.footer-table {
  width: 100%;
}
.footer-table ul {
  list-style: none;
}
/*============ BOOTSTRAP BREAK POINTS:

Extra small (xs) devices (portrait phones, less than 576px)
No media query since this is the default in Bootstrap

Small (sm) devices (landscape phones, 576px and up)
@media (min-width: 576px) { ... }

Medium (md) devices (tablets, 768px and up)
@media (min-width: 768px) { ... }

Large (lg) devices (desktops, 992px and up)
@media (min-width: 992px) { ... }

Extra (xl) large devices (large desktops, 1200px and up)
@media (min-width: 1200px) { ... }

=============*/

/*-- Fixed Landing Page Section --*/
.landing {
  position: relative;
  width: 100%;
  display: table;
}

.home-inner {
  display: table;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

/*============Seat============*/
*,
*:before,
*:after {
  box-sizing: border-box;
}
.place-seat {
  background-color: rgb(183, 183, 219);
}
.comf-res {
  background-image: url("./images/ticket.jpg");
  background-color: #a0b7ee;
  padding: 10px 50px;
  display: inline-block;
  border: none;
  border-radius: 10px;
  box-sizing: border-box;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  color: #ffffff;
  text-align: center;
  transition: all 0.5s;
  margin-left: 588px;
  margin-top: 13px;
  margin-bottom: 15px;
  outline: none !important;
}
.comf-res:hover {
  background-color: #134acc;
}
.Siege {
  margin-left: 508px;
  margin-top: 120px;
  position: absolute;
}
.Siege span {
  padding-left: 10px;
  font-weight: bolder;
}
.s-disp {
  position: relative;
  width: 27px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5rem;
  padding: 5px 10px;
  background: #0976f3;
  border-radius: 5px;
  color: black;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.s-nondisp {
  position: relative;
  width: 27px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5rem;
  padding: 5px 10px;
  background: #dddddd;
  color: rgba(0, 0, 0, 0.24);
  overflow: hidden;
  border-radius: 5px;
  color: black;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.s-sel {
  position: relative;
  width: 27px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5rem;
  padding: 5px 10px;
  background: #bada55;
  border-radius: 5px;
  color: black;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
}
html {
  font-size: 15px;
}
.res-place {
  position: absolute;
  margin-top: 75px;
  margin-left: 590px;
  font-weight: 900;
  color: rgb(4, 55, 121);
  font-family: "Gill Sans", "Gill Sans MT", Calibri, "Trebuchet MS", sans-serif;
}
.plane {
  margin-left: 43%;
  max-width: 250px;
}

.cockpit {
  height: 220px;
  position: relative;
  overflow: hidden;
  text-align: center;
  border-bottom: 5px solid #d43a0b;
}
.cockpit:before {
  content: "";
  display: block;
  position: absolute;
  top: 150px;
  left: 0;
  height: 400px;
  width: 100%;
  border-right: 5px solid #d43a0b;
  border-left: 5px solid #d43a0b;
  border-top: 5px solid #d43a0b;
  border-radius: 30px;
}
.cockpit h1 {
  width: 40%;
  margin-top: 163px;
  margin-left: 1px;
}

.fuselage {
  border-right: 5px solid #d43a0b;
  border-left: 5px solid #d43a0b;
  border-bottom: 5px solid #d43a0b;
}

ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

.seats {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.seat {
  display: flex;
  flex: 0 0 14.2857142857%;
  padding-left: 20px;
  padding-top: 10px;
  padding-right: 10px;
  position: relative;
}
.seat:nth-child(2) {
  margin-right: 14.2857142857%;
}
.seat input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}
.seat input[type="checkbox"]:checked + label {
  background: #bada55;
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
  animation-duration: 300ms;
  animation-fill-mode: both;
}
.seat input[type="checkbox"]:disabled + label {
  background: #dddddd;
  color: rgba(0, 0, 0, 0.24);
  overflow: hidden;
}
.seat input[type="checkbox"]:disabled + label:after {
  content: "";
  text-indent: 0;
  position: absolute;
  top: 4px;
  left: 50%;
  color: black;
  transform: translate(-50%, 0%);
}
.seat input[type="checkbox"]:disabled + label:hover {
  box-shadow: none;
  cursor: not-allowed;
}
.seat label {
  display: block;
  position: relative;
  width: 27px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5rem;
  padding: 5px 0px;
  background: #0976f3;
  border-radius: 5px;
  animation-duration: 300ms;
  animation-fill-mode: both;
  color: black;
}

.seat label:before {
  content: "";
  position: absolute;
  width: 75%;
  height: 75%;
  top: 1px;
  left: 50%;
  transform: translate(-50%, 0%);
  background: rgba(255, 255, 255, 0);
  border-radius: 3px;
}
.seat label:hover {
  cursor: pointer;
  box-shadow: 0 0 0px 2px #5c6aff;
}

@-webkit-keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
}

@media (max-width: 767px) {
  .dropdown-menu {
    transform: translate(0%);
  }
  .cart-icon span {
    position: absolute;
    top: 6px;
    margin-right: 95%;
    right: 0px;
  }
  .accordion {
    left: 0px;
    right: 500px;
  }
  .ticket {
    width: 340px;
    background-color: rgba(32, 27, 27, 0.822);
    background-position: -45px -80px;
    margin-top: 4%;

    color: white;
    box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
    list-style: none;
    font-weight: bold;
    padding-left: 0;
    margin-left: 22%;
    border-radius: 20px;
    justify-content: center;
  }
}
@media (max-width: 1260px) {
  .cards_items {
    display: block;
    padding: 0px;
  }
  .cards_wrapper {
    margin-left: 33%;
  }
}
@media (max-width: 1200px) {
  .select-box-second {
    display: block;
    text-align: center;
  }
  .about_items td {
    display: block;
  }
  .cards_items {
    display: block;
    padding: 0px;
  }
  .cards_item {
    margin-top: 13px;
  }
  .about-text {
    margin-left: 38%;
  }
  .about-title {
    margin-left: 40%;
  }
  .select-box-second {
    display: block;
    text-align: center;
  }
  .cards_items {
    display: block;
    padding: 0px;
  }
  .cards_wrapper {
    margin-left: 25%;
  }
  .cards_item {
    margin-top: 13px;
  }
  .Vcontainer-second {
    margin-top: 50px;
  }
  .selected-second,
  .Date-second,
  .Search-btn-second {
    text-align: center;
    margin-top: 10px;
    margin-left: 60px;
    width: 200px;
    border: none;
    justify-content: center;
  }
  .secondpage table {
    margin-top: 2%;
    width: 90%;
  }
  .secondpage table td {
    color: black;
    width: 80%;
    padding: 0px;
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
    margin-left: 15%;
    padding-top: 5px;
    /* border: black solid ; */
    border-collapse: separate;
  }
}
@media (max-width: 1000px) {
  .cards_wrapper {
    margin-left: 25%;
  }
}
@media (max-width: 848px) {
  .cards_wrapper {
    margin-left: 20%;
  }
}
@media (max-width: 694px) {
  .title-voyage {
    margin-left: 2%;
  }
  .cards_wrapper {
    margin-left: 10%;
  }
}

@media (max-width: 600px) {
  .dateError {
    margin-left: 30%;
  }
}

@media (max-width: 576px) {
  .dateError {
    margin-left: 30%;
  }
  .Vcontainer {
    margin-top: 33%;
    text-align: center;
    z-index: -1;
  }
  .cart-icon span {
    position: absolute;
    top: 6px;
    margin-right: 95%;
  }

  .about_items td {
    display: block;
  }
  .cards_items {
    display: block;
    padding: 0px;
  }
  .cards_wrapper {
    margin-left: 0%;
  }
  .cards_item {
    margin-top: 13px;
  }
  .about-text {
    margin-left: 63px;
  }
  .about-title {
    margin-left: 95px;
  }
  .select-box-second {
    display: block;
    text-align: center;
  }
  .Vcontainer-second {
    margin-top: 50px;
  }
  .selected-second,
  .Date-second,
  .Search-btn-second {
    text-align: center;
    margin-top: 10px;
    margin-left: 60px;
    width: 200px;
    border: none;
    justify-content: center;
  }
  .secondpage table {
    margin-top: 2%;
    width: 90%;
  }
  .secondpage table td {
    color: black;
    width: 80%;
    padding: 0px;
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
    margin-left: 57px;
    padding-top: 5px;
    /* border: black solid ; */
    border-collapse: separate;
  }
  .reservation {
    text-align: center;
    background: linear-gradient(to right, rgb(14, 198, 211), rgb(58, 70, 172));
    margin-top: 150px;
    box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
    border-radius: 50px;
    grid-template-columns: 1fr;
    width: 90%;
    padding-right: 40px;
  }
  .ticket {
    width: 300px;
    background-color: rgba(32, 27, 27, 0.822);
    background-position: -45px -80px;
    margin-top: 4%;

    color: white;
    box-shadow: 0 5px 8px 0 rgba(0, 0, 0, 0.2), 0 7px 20px 0 rgba(0, 0, 0, 0.2);
    list-style: none;
    font-weight: bold;
    padding-left: 0;
    margin-left: 22%;
    border-radius: 20px;
    justify-content: center;
  }
  .footer-up {
    padding: 50px;
    padding-right: 30px;
    color: black;
  }
}
@media (max-device-width: 784px) {
  .footer-up {
    padding: 50px;
    padding-right: 30px;
    color: black;
  }
}
