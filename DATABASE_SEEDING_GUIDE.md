# Database Seeding Guide for <PERSON><PERSON><PERSON><PERSON> (Morocco Travel Booking System)

This guide will help you populate your database with realistic Moroccan travel data.

## What's Included

The seeding system creates:

### 1. **Cities (Villes)** - 15 major Moroccan cities
- Casablanca (الدار البيضاء)
- Rabat (الرباط) 
- Marrakech (مراكش)
- <PERSON><PERSON> (فاس)
- <PERSON><PERSON> (طنجة)
- <PERSON><PERSON><PERSON> (أكادير)
- <PERSON><PERSON><PERSON><PERSON> (مكناس)
- <PERSON><PERSON>jda (وجدة)
- <PERSON><PERSON><PERSON><PERSON> (تطوان)
- Salé (سلا)
- Kénitra (القنيطرة)
- El Jadida (الجديدة)
- Nador (الناظور)
- Settat (سطات)
- <PERSON><PERSON> (العرائش)

### 2. **Travel Agencies (Agences)** - 10 real Moroccan bus companies
- CTM (شركة النقل المغربية)
- Supratours (سوبراتورز)
- ALSA (ألسا)
- <PERSON><PERSON><PERSON><PERSON> (غزالة)
- SATAS (ساتاس)
- <PERSON><PERSON><PERSON> du Maroc (بولمان المغرب)
- <PERSON> Ghazala (ترانس غزالة)
- <PERSON><PERSON><PERSON> (نجمة الشمال)
- <PERSON><PERSON> (تاسلا)
- Stareo (ستاريو)

### 3. **Voyages** - Popular routes between major cities
- Casablanca ↔ Rabat
- Marrakech ↔ Casablanca  
- Fès ↔ Meknès
- Tanger ↔ Tétouan
- Agadir ↔ Marrakech
- Rabat ↔ Fès
- Oujda ↔ Fès

### 4. **Bus Seats (Places)** - 50-seat bus configurations
### 5. **Users** - Sample Moroccan users with realistic names
### 6. **Reservations** - Sample bookings
### 7. **Admin Users** - Default admin accounts

## Commands to Run

### Step 1: Fresh Database Setup (if needed)
```bash
# Reset database and run migrations
php artisan migrate:fresh
```

### Step 2: Seed the Database
```bash
# Run all seeders
php artisan db:seed
```

### Alternative: Fresh Migration + Seeding in One Command
```bash
# Reset database, run migrations, and seed data
php artisan migrate:fresh --seed
```

### Step 3: Verify Data
```bash
# Check if data was created successfully
php artisan tinker
```

Then in tinker:
```php
// Check cities
App\Models\Ville::count();
App\Models\Ville::first();

// Check agencies  
App\Models\Agence::count();

// Check voyages
App\Models\Voyage::count();

// Check users
App\Models\User::count();

// Exit tinker
exit
```

## Default Admin Credentials

After seeding, you can login to the admin panel with:

- **Email:** <EMAIL>
- **Password:** password123

Alternative admin account:
- **Email:** <EMAIL>  
- **Password:** password123

## Individual Seeders

You can also run individual seeders if needed:

```bash
# Seed only cities
php artisan db:seed --class=VilleSeeder

# Seed only agencies
php artisan db:seed --class=AgenceSeeder

# Seed only admin users
php artisan db:seed --class=AdminSeeder

# Seed only voyages
php artisan db:seed --class=VoyageSeeder
```

## Factories Available

You can also use the factories directly in tinker or your own seeders:

```php
// Create 10 users
App\Models\User::factory(10)->create();

// Create 5 voyages
App\Models\Voyage::factory(5)->create();

// Create bus with all seats available
App\Models\Places::factory()->allAvailable()->create();

// Create bus with most seats occupied
App\Models\Places::factory()->mostlyOccupied()->create();

// Create 20 reservations
App\Models\Reservation::factory(20)->create();
```

## Troubleshooting

### If you get foreign key constraint errors:
```bash
# Make sure to seed in the correct order
php artisan db:seed --class=VilleSeeder
php artisan db:seed --class=AgenceSeeder  
php artisan db:seed --class=VoyageSeeder
```

### If you want to reset and start over:
```bash
php artisan migrate:fresh --seed
```

### To check what's in your database:
```bash
php artisan tinker
```

## What Users Can Do After Seeding

With this data, users will be able to:

1. **Browse available routes** between major Moroccan cities
2. **Select travel agencies** from real Moroccan bus companies
3. **Choose departure times** (multiple times per route)
4. **View seat availability** (50-seat bus layout)
5. **Make reservations** for 1-4 passengers
6. **See prices** in Moroccan Dirham equivalent

The system now has realistic data that represents the actual Moroccan intercity bus travel market!
