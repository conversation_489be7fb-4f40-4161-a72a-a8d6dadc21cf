{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"axios": "^0.21", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.1.14", "@material-ui/core": "^4.11.4", "@material-ui/lab": "^4.0.0-alpha.58", "@testing-library/jest-dom": "^5.12.0", "@testing-library/react": "^11.2.6", "@testing-library/user-event": "^12.8.3", "aos": "^2.3.4", "bootstrap": "^4.6.0", "emailjs-com": "^3.0.2", "ra-data-simple-rest": "^3.13.4", "react": "^17.0.2", "react-admin": "^3.15.0", "react-autocomplete": "^1.8.1", "react-bootstrap": "^1.6.0", "react-calendar": "^3.4.0", "react-datepicker": "^3.8.0", "react-dom": "^17.0.2", "react-hook-form": "^7.7.1", "react-icons": "^4.2.0", "react-modal": "^3.13.1", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "redux": "^4.1.0", "styled-components": "^5.3.0", "web-vitals": "^1.1.2", "yup": "^0.32.9"}}