import React, { useState, useEffect }  from 'react'
import {<PERSON><PERSON><PERSON><PERSON>outer as Router,Link, Redirect, useHistory} from 'react-router-dom'
import {Modal, Button, Row, Col, Form} from 'react-bootstrap'
const Reclamation = ({reclamations,setReclamations,user}) => {
  const handleClose = () => {setReclamations(false);setSign(false);setSign2(false)};
  const handleShow = () => {setReclamations(true);setReclamation("")};

  const [reclamation, setReclamation] = useState("")
  const [sign, setSign] = useState(false)
  const [sign2, setSign2] = useState(false)
  const [user_id, setId] = useState(user.user.id)

useEffect(() => {
    setId(user.user.id)
   },[reclamations])
  const submit=async(e)=>{
    e.preventDefault()

    if(reclamation==""){
      setSign(true)
      setSign2(false)
    }
 else{
  setSign2(true);
  setSign(false);
  setReclamation("");
  const response= await fetch('http://localhost:8000/api/Reclamer',{
    method: 'POST',
    headers:{'Content-Type':'application/json'},

    body: JSON.stringify({
        reclamation,
        user_id,
    })
});
const content = await response.json();




    }
    } 
    return (
        <div>  
  
      <a variant="primary" role="button" onClick={handleShow}>
        Réclamation
      </a>

      <Modal show={reclamations} onHide={handleClose} className="Recl-modal">
        <Modal.Header>
          <Modal.Title>Réclamation!</Modal.Title>
          <i class="fas fa-window-close close-btn"onClick={handleClose}></i>
        </Modal.Header>
        <Modal.Body>
        <form className="px-4 py-3" onSubmit={submit}>
    <div>
      <label>Saisir votre réclamation:</label>
      <br></br>
      <textarea className="textrecl" rows="10" cols="50" placeholder="Réclamation...." onChange={(e)=>setReclamation(e.target.value)}></textarea>
    </div>
  <input type="submit" class="btnlogin" value="Envoyer"/>
    <div className="sign-confirm">{sign?"Réclamation Vide !":""}</div>
    <div className="sign-confirm">{sign2?"Réclamation Envoyée !":""}</div>
  </form>
  

        </Modal.Body>
        <Modal.Footer>
<Button variant="secondary" onClick={handleClose}>
            Fermer
          </Button> 
        </Modal.Footer>
      </Modal>

</div>
    )
}

export default Reclamation
