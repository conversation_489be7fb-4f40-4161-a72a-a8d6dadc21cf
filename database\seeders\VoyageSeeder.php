<?php

namespace Database\Seeders;

use App\Models\Voyage;
use App\Models\Ville;
use App\Models\Agence;
use App\Models\Places;
use Illuminate\Database\Seeder;

class VoyageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing voyages and related places to avoid duplicates
        \DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Voyage::truncate();
        Places::truncate();
        \DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $routes = [
            ['from' => 'Casablanca', 'to' => 'Rabat', 'ligne_fr' => 'Casablanca - Rabat', 'ligne_ar' => 'الدار البيضاء - الرباط'],
            ['from' => 'Rabat', 'to' => 'Casablanca', 'ligne_fr' => 'Rabat - Casablanca', 'ligne_ar' => 'الرباط - الدار البيضاء'],
            ['from' => 'Marrakech', 'to' => 'Casablanca', 'ligne_fr' => 'Marrakech - Casablanca', 'ligne_ar' => 'مراكش - الدار البيضاء'],
            ['from' => 'Casablanca', 'to' => 'Marrakech', 'ligne_fr' => 'Casablanca - Marrakech', 'ligne_ar' => 'الدار البيضاء - مراكش'],
            ['from' => 'Fès', 'to' => 'Meknès', 'ligne_fr' => 'Fès - Meknès', 'ligne_ar' => 'فاس - مكناس'],
            ['from' => 'Meknès', 'to' => 'Fès', 'ligne_fr' => 'Meknès - Fès', 'ligne_ar' => 'مكناس - فاس'],
            ['from' => 'Tanger', 'to' => 'Tétouan', 'ligne_fr' => 'Tanger - Tétouan', 'ligne_ar' => 'طنجة - تطوان'],
            ['from' => 'Tétouan', 'to' => 'Tanger', 'ligne_fr' => 'Tétouan - Tanger', 'ligne_ar' => 'تطوان - طنجة'],
            ['from' => 'Agadir', 'to' => 'Marrakech', 'ligne_fr' => 'Agadir - Marrakech', 'ligne_ar' => 'أكادير - مراكش'],
            ['from' => 'Marrakech', 'to' => 'Agadir', 'ligne_fr' => 'Marrakech - Agadir', 'ligne_ar' => 'مراكش - أكادير'],
            ['from' => 'Rabat', 'to' => 'Fès', 'ligne_fr' => 'Rabat - Fès', 'ligne_ar' => 'الرباط - فاس'],
            ['from' => 'Fès', 'to' => 'Rabat', 'ligne_fr' => 'Fès - Rabat', 'ligne_ar' => 'فاس - الرباط'],
            ['from' => 'Oujda', 'to' => 'Fès', 'ligne_fr' => 'Oujda - Fès', 'ligne_ar' => 'وجدة - فاس'],
            ['from' => 'Fès', 'to' => 'Oujda', 'ligne_fr' => 'Fès - Oujda', 'ligne_ar' => 'فاس - وجدة'],
            ['from' => 'Casablanca', 'to' => 'Tanger', 'ligne_fr' => 'Casablanca - Tanger', 'ligne_ar' => 'الدار البيضاء - طنجة'],
            ['from' => 'Tanger', 'to' => 'Casablanca', 'ligne_fr' => 'Tanger - Casablanca', 'ligne_ar' => 'طنجة - الدار البيضاء'],
            ['from' => 'Casablanca', 'to' => 'Agadir', 'ligne_fr' => 'Casablanca - Agadir', 'ligne_ar' => 'الدار البيضاء - أكادير'],
            ['from' => 'Agadir', 'to' => 'Casablanca', 'ligne_fr' => 'Agadir - Casablanca', 'ligne_ar' => 'أكادير - الدار البيضاء'],
        ];

        $departTimes = ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'];

        $agences = Agence::all();
        $villes = Ville::all();

        // Create one voyage per route to avoid duplicates
        foreach ($routes as $index => $route) {
            // Find the destination city
            $destinationVille = $villes->where('nom_fr', $route['to'])->first();

            if ($destinationVille) {
                // Create a new Places record for each voyage
                $places = Places::factory()->allAvailable()->create();

                // Use different agencies and times for variety
                $agence = $agences->get($index % $agences->count());
                $departTime = $departTimes[$index % count($departTimes)];

                Voyage::create([
                    'titre' => 'Voyage ' . $route['ligne_fr'],
                    'agence_id' => $agence->id,
                    'ville_id' => $destinationVille->id,
                    'places_id' => $places->id,
                    'depart' => $departTime,
                    'ligne_fr' => $route['ligne_fr'],
                    'ligne_ar' => $route['ligne_ar'],
                    'active' => true,
                ]);
            }
        }
    }
}
