<?php

namespace Database\Seeders;

use App\Models\Voyage;
use App\Models\Ville;
use App\Models\Agence;
use App\Models\Places;
use Illuminate\Database\Seeder;

class VoyageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $routes = [
            ['from' => 'Casablanca', 'to' => 'Rabat', 'ligne_fr' => 'Casablanca - Rabat', 'ligne_ar' => 'الدار البيضاء - الرباط'],
            ['from' => 'Rabat', 'to' => 'Casablanca', 'ligne_fr' => 'Rabat - Casablanca', 'ligne_ar' => 'الرباط - الدار البيضاء'],
            ['from' => 'Marrakech', 'to' => 'Casablanca', 'ligne_fr' => 'Marrakech - Casablanca', 'ligne_ar' => 'مراكش - الدار البيضاء'],
            ['from' => 'Casablanca', 'to' => 'Marrakech', 'ligne_fr' => 'Casablanca - Marrakech', 'ligne_ar' => 'الدار البيضاء - مراكش'],
            ['from' => 'Fès', 'to' => 'Meknès', 'ligne_fr' => 'Fès - Meknès', 'ligne_ar' => 'فاس - مكناس'],
            ['from' => 'Meknès', 'to' => 'Fès', 'ligne_fr' => 'Meknès - Fès', 'ligne_ar' => 'مكناس - فاس'],
            ['from' => 'Tanger', 'to' => 'Tétouan', 'ligne_fr' => 'Tanger - Tétouan', 'ligne_ar' => 'طنجة - تطوان'],
            ['from' => 'Tétouan', 'to' => 'Tanger', 'ligne_fr' => 'Tétouan - Tanger', 'ligne_ar' => 'تطوان - طنجة'],
            ['from' => 'Agadir', 'to' => 'Marrakech', 'ligne_fr' => 'Agadir - Marrakech', 'ligne_ar' => 'أكادير - مراكش'],
            ['from' => 'Marrakech', 'to' => 'Agadir', 'ligne_fr' => 'Marrakech - Agadir', 'ligne_ar' => 'مراكش - أكادير'],
            ['from' => 'Rabat', 'to' => 'Fès', 'ligne_fr' => 'Rabat - Fès', 'ligne_ar' => 'الرباط - فاس'],
            ['from' => 'Fès', 'to' => 'Rabat', 'ligne_fr' => 'Fès - Rabat', 'ligne_ar' => 'فاس - الرباط'],
            ['from' => 'Oujda', 'to' => 'Fès', 'ligne_fr' => 'Oujda - Fès', 'ligne_ar' => 'وجدة - فاس'],
            ['from' => 'Fès', 'to' => 'Oujda', 'ligne_fr' => 'Fès - Oujda', 'ligne_ar' => 'فاس - وجدة'],
        ];

        $departTimes = ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'];
        
        $agences = Agence::all();
        $villes = Ville::all();

        foreach ($routes as $route) {
            // Find the destination city
            $destinationVille = $villes->where('nom_fr', $route['to'])->first();
            
            if ($destinationVille) {
                // Create 2-3 voyages per route with different times and agencies
                for ($i = 0; $i < rand(2, 3); $i++) {
                    // Create a new Places record for each voyage
                    $places = Places::factory()->create();
                    
                    Voyage::create([
                        'titre' => 'Voyage ' . $route['ligne_fr'],
                        'agence_id' => $agences->random()->id,
                        'ville_id' => $destinationVille->id,
                        'places_id' => $places->id,
                        'depart' => $departTimes[array_rand($departTimes)],
                        'ligne_fr' => $route['ligne_fr'],
                        'ligne_ar' => $route['ligne_ar'],
                        'active' => true,
                    ]);
                }
            }
        }
    }
}
