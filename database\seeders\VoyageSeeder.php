<?php

namespace Database\Seeders;

use App\Models\Voyage;
use App\Models\Ville;
use App\Models\Agence;
use App\Models\Places;
use Illuminate\Database\Seeder;

class VoyageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $routes = [
            ['from' => 'Tanger', 'to' => 'Casablanca', 'ligne_fr' => 'Tanger - Casablanca', 'ligne_ar' => 'طنجة - الدار البيضاء'],
            ['from' => 'Tanger', 'to' => 'Rabat', 'ligne_fr' => 'Tanger - Rabat', 'ligne_ar' => 'طنجة - الرباط'],
            ['from' => 'Tanger', 'to' => 'Marrakech', 'ligne_fr' => 'Tanger - Marrakech', 'ligne_ar' => 'طنجة - مراكش'],
            ['from' => 'Tanger', 'to' => 'Fès', 'ligne_fr' => 'Tanger - Fès', 'ligne_ar' => 'طنجة - فاس'],
            ['from' => 'Tanger', 'to' => 'Meknès', 'ligne_fr' => 'Tanger - Meknès', 'ligne_ar' => 'طنجة - مكناس'],
            ['from' => 'Tanger', 'to' => 'Tétouan', 'ligne_fr' => 'Tanger - Tétouan', 'ligne_ar' => 'طنجة - تطوان'],
            ['from' => 'Tanger', 'to' => 'Agadir', 'ligne_fr' => 'Tanger - Agadir', 'ligne_ar' => 'طنجة - أكادير'],
            ['from' => 'Tanger', 'to' => 'Oujda', 'ligne_fr' => 'Tanger - Oujda', 'ligne_ar' => 'طنجة - وجدة'],
            ['from' => 'Tanger', 'to' => 'Salé', 'ligne_fr' => 'Tanger - Salé', 'ligne_ar' => 'طنجة - سلا'],
            ['from' => 'Tanger', 'to' => 'Kénitra', 'ligne_fr' => 'Tanger - Kénitra', 'ligne_ar' => 'طنجة - القنيطرة'],
            ['from' => 'Tanger', 'to' => 'El Jadida', 'ligne_fr' => 'Tanger - El Jadida', 'ligne_ar' => 'طنجة - الجديدة'],
            ['from' => 'Tanger', 'to' => 'Nador', 'ligne_fr' => 'Tanger - Nador', 'ligne_ar' => 'طنجة - الناظور'],
            ['from' => 'Tanger', 'to' => 'Settat', 'ligne_fr' => 'Tanger - Settat', 'ligne_ar' => 'طنجة - سطات'],
            ['from' => 'Tanger', 'to' => 'Larache', 'ligne_fr' => 'Tanger - Larache', 'ligne_ar' => 'طنجة - العرائش'],
        ];

        $departTimes = ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00'];

        $agences = Agence::all();
        $villes = Ville::all();

        // Create one voyage per route to avoid duplicates
        foreach ($routes as $index => $route) {
            // Find the destination city
            $destinationVille = $villes->where('nom_fr', $route['to'])->first();

            if ($destinationVille) {
                // Create a new Places record for each voyage
                $places = Places::factory()->allAvailable()->create();

                // Use different agencies and times for variety
                $agence = $agences->get($index % $agences->count());
                $departTime = $departTimes[$index % count($departTimes)];

                Voyage::create([
                    'titre' => 'Voyage ' . $route['ligne_fr'],
                    'agence_id' => $agence->id,
                    'ville_id' => $destinationVille->id,
                    'places_id' => $places->id,
                    'depart' => $departTime,
                    'ligne_fr' => $route['ligne_fr'],
                    'ligne_ar' => $route['ligne_ar'],
                    'active' => true,
                ]);
            }
        }
    }
}
