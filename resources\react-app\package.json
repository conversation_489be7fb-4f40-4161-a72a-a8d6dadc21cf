{"name": "admin-test", "version": "0.1.0", "private": true, "dependencies": {"@material-ui/core": "^4.11.4", "@material-ui/lab": "^4.0.0-alpha.58", "@testing-library/jest-dom": "^5.12.0", "@testing-library/react": "^11.2.6", "@testing-library/user-event": "^12.8.3", "aos": "^2.3.4", "axios": "^0.21.4", "bootstrap": "^4.6.0", "emailjs-com": "^3.0.2", "ra-data-simple-rest": "^3.13.4", "react": "^17.0.2", "react-admin": "^3.15.0", "react-autocomplete": "^1.8.1", "react-bootstrap": "^1.6.0", "react-calendar": "^3.4.0", "react-datepicker": "^3.8.0", "react-dom": "^17.0.2", "react-hook-form": "^7.7.1", "react-icons": "^4.2.0", "react-modal": "^3.13.1", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "redux": "^4.1.0", "styled-components": "^5.3.0", "watchpack": "^2.4.0", "web-vitals": "^1.1.2", "yup": "^0.32.9"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}